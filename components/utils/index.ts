export const calculateCode = (codEmpreendimentoId: string) => {
  const dataNow = new Date();
  const mes = dataNow.getMonth() + 1;
  const dia = dataNow.getDate();
  const empreendimentoId = Number(codEmpreendimentoId);

  const verific1 = 4 * empreendimentoId + 2 * mes + 4 * dia;
  const verific2 = Math.abs(1 * empreendimentoId + 5 * dia - 6 * mes);
  const verific3 = Math.abs(6 * empreendimentoId + 5 * mes - 4 * dia);
  const verific4 = Math.abs(2 * empreendimentoId - 7 * dia + 4 * mes);

  return `${verific1}${mes}${verific2}${verific3}${verific4}${dia}`;
};
