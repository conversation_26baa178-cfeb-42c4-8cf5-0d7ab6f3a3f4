import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
    getCurrentEnvironment,
    getEnvironmentDisplayName,
    isProduction
} from '../constants/Environment';

export const EnvironmentTag: React.FC = () => {
  const insets = useSafeAreaInsets();
  const environment = getCurrentEnvironment();

  // Não mostra a tag em produção
  if (isProduction()) {
    return null;
  }

  const getTagStyle = () => {
    switch (environment) {
      case 'development':
        return {
          backgroundColor: '#FF6B35', // Laranja para desenvolvimento
          borderColor: '#E55A2B',
        };
      case 'staging':
        return {
          backgroundColor: '#4ECDC4', // Verde-azulado para staging
          borderColor: '#45B7B8',
        };
      default:
        return {
          backgroundColor: '#95A5A6', // Cinza para outros
          borderColor: '#7F8C8D',
        };
    }
  };

  const tagStyle = getTagStyle();
  const displayName = getEnvironmentDisplayName();

  return (
    <View
      testID="environment-tag-container"
      style={[
        styles.container,
        {
          top: insets.top + 10, // Respeita a safe area + margem
        }
      ]}
      pointerEvents="none" // Permite toques passarem através da tag
    >
      <View style={[styles.tag, tagStyle]} testID="environment-tag">
        <Text style={styles.text} testID="environment-tag-text">{displayName}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 10,
    zIndex: 9999, // Garante que fica por cima de tudo
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5, // Para Android
  },
  text: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
});

export default EnvironmentTag;
