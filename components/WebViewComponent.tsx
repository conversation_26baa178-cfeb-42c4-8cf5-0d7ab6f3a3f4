// import React, { useEffect, useRef } from 'react';
// import { WebView } from 'react-native-webview';

// import { ALLOWED_DOMAINS } from '@/constants/allowedDomains';
// import { useAuth } from '@/hooks/useAuth';
// import { useWebViewHandlers } from '@/hooks/useWebViewHandlers';

// interface WebViewComponentProps {
//   url: string;
//   onMessage: (event: any) => void;
// }

// export const WebViewComponent: React.FC<WebViewComponentProps> = ({ url, onMessage }) => {
//   const webViewRef = useRef<WebView>(null);
//   const { isAuthenticated, initializeAuth } = useAuth(webViewRef);
//   const { handleMessage } = useWebViewHandlers(webViewRef);

//   useEffect(() => {
//     if (webViewRef.current) {
//       console.log('::: WebViewComponent >> webViewRef.current', webViewRef.current);
//       initializeAuth();
//     }
//   }, [webViewRef.current]);

//   const handleRequest = (request: any) => {
//     return ALLOWED_DOMAINS.some((domain) => request.url.startsWith(domain));
//   };

//   console.log('url');
//   console.log(url);

//   return (
//     <WebView
//       ref={webViewRef}
//       javaScriptEnabled={true}
//       allowsInlineMediaPlayback={true}
//       // allow="microphone; camera;"
//       // setLoadWithOverviewMode={true}
//       source={{ uri: url }}
//       injectedJavaScript={`document.documentElement.lang = 'pt-BR';`}
//       onShouldStartLoadWithRequest={handleRequest}
//       onMessage={handleMessage}
//       incognito={!isAuthenticated}
//       cacheEnabled={true}
//     />
//   );
// };

// export default WebViewComponent;

import React, { useEffect, useRef } from 'react';
import { WebView } from 'react-native-webview';

import { ALLOWED_DOMAINS } from '@/constants/allowedDomains';
import { useAuth } from '@/hooks/useAuth';
import { useWebViewHandlers } from '@/hooks/useWebViewHandlers';

interface WebViewComponentProps {
  url: string;
  onMessage: (event: any) => void;
}

export const WebViewComponent: React.FC<WebViewComponentProps> = ({ url, onMessage }) => {
  const webViewRef = useRef<WebView>(null);
  const { isAuthenticated, initializeAuth } = useAuth(webViewRef);
  const { handleMessage } = useWebViewHandlers(webViewRef);

  useEffect(() => {
    if (webViewRef.current) {
      console.log('::: WebViewComponent >> webViewRef.current', webViewRef.current);
      initializeAuth();
    }
  }, [webViewRef.current]);

  const handleRequest = (request: any) => {
    return ALLOWED_DOMAINS.some((domain) => request.url.startsWith(domain));
  };

  // JavaScript a ser injetado para capturar logs e erros
  const debuggingScript = `
    (function() {
      // Armazena as funções originais do console
      const originalConsoleLog = console.log;
      const originalConsoleError = console.error;
      const originalConsoleWarn = console.warn;
      const originalConsoleInfo = console.info;

      // Sobrescreve console.log
      console.log = function() {
        // Chama a função original
        originalConsoleLog.apply(console, arguments);

        // Envia para o app nativo
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: 'webview-debug',
            logType: 'log',
            data: Array.from(arguments).map(arg => {
              try {
                // Tenta converter objetos em strings
                return typeof arg === 'object' ? JSON.stringify(arg) : String(arg);
              } catch (e) {
                return String(arg);
              }
            })
          })
        );
      };

      // Sobrescreve console.error
      console.error = function() {
        originalConsoleError.apply(console, arguments);
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: 'webview-debug',
            logType: 'error',
            data: Array.from(arguments).map(arg => {
              try {
                return typeof arg === 'object' ? JSON.stringify(arg) : String(arg);
              } catch (e) {
                return String(arg);
              }
            })
          })
        );
      };

      // Sobrescreve console.warn
      console.warn = function() {
        originalConsoleWarn.apply(console, arguments);
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: 'webview-debug',
            logType: 'warn',
            data: Array.from(arguments).map(arg => {
              try {
                return typeof arg === 'object' ? JSON.stringify(arg) : String(arg);
              } catch (e) {
                return String(arg);
              }
            })
          })
        );
      };

      // Captura erros não tratados
      window.onerror = function(message, source, line, column, error) {
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: 'webview-debug',
            logType: 'uncaught-error',
            data: {
              message,
              source,
              line,
              column,
              stack: error ? error.stack : ''
            }
          })
        );
        return true;
      };

      // Define o idioma conforme seu código original
      document.documentElement.lang = 'pt-BR';
    })();
    true;
  `;

  console.log('url');
  console.log(url);

  return (
    <WebView
      ref={webViewRef}
      javaScriptEnabled={true}
      allowsInlineMediaPlayback={true}
      // allow="microphone; camera;"
      // setLoadWithOverviewMode={true}
      source={{ uri: url }}
      injectedJavaScript={debuggingScript}
      onShouldStartLoadWithRequest={handleRequest}
      onMessage={handleMessage}
      incognito={!isAuthenticated}
      cacheEnabled={true}
      // Adicione esta propriedade para Android
      // debuggingEnabled={true}
    />
  );
};

export default WebViewComponent;
