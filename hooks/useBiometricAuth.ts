import { MutableRefObject, useContext } from 'react';
import { Alert, Linking, Platform } from 'react-native';
import * as LocalAuthentication from 'expo-local-authentication';
import { useAuth } from './useAuth';
import WebView from 'react-native-webview';

export const useBiometricAuth = (webViewRef: MutableRefObject<WebView | null>) => {
    const { setIsAuthenticated, authWithToken, loadToken } = useAuth(webViewRef);

    const checkAvailableAuthentication = async () => {
        const compatible = await LocalAuthentication.hasHardwareAsync();
        console.log('compatible', compatible);

        if (compatible) {
            const supportedAuthTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
            console.log('supportedAuthTypes', supportedAuthTypes.map((type) => LocalAuthentication.AuthenticationType[type]));
        }

        return compatible;
    };

    const handleAuthentication = async () => {
        const isBiometricEnrolled = await LocalAuthentication.isEnrolledAsync();
        if (!isBiometricEnrolled) {
            return Alert.alert(
                'Biometria não cadastrada',
                'Por favor, cadastre sua biometria nas configurações do dispositivo.',
                [
                    {
                        text: 'Cancelar',
                        style: 'cancel',
                    },
                    {
                        text: 'Configurações',
                        onPress: () => {
                            if (Platform.OS === 'ios') {
                                Linking.openURL('App-Prefs:root=TOUCHID_PASSCODE');
                            } else {
                                Linking.openURL('package:com.android.settings.biometrics');
                            }
                        },
                    },
                ]
            );
        }

        const token = await loadToken();
        if (!token) return false;

        const result = await LocalAuthentication.authenticateAsync({
            promptMessage: 'Confirme sua identidade',
            fallbackLabel: 'Identidade não reconhecida. Tente novamente.',
            cancelLabel: 'Cancelar',
            disableDeviceFallback: true,
        });
        
        console.log('::: result', result);
        if (result.success) authWithToken();

        setIsAuthenticated(result.success);

        return result.success;
    };

    const authenticate = async () => {
        try {
            const hasHardware = await checkAvailableAuthentication();
            if (!hasHardware) {
                return false;
            }

            return handleAuthentication();
        } catch (err) {
            console.error('Biometric error:', err);
            return false;
        }
    };

    return { authenticate };
};
