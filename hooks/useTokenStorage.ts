import { useState, useEffect } from 'react';
import * as SecureStore from 'expo-secure-store';

export const useTokenStorage = () => {
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const loadToken = async () => {
      const storedToken = await SecureStore.getItemAsync('userToken');
      if (storedToken) {
        setToken(storedToken);
      }
    };
    loadToken();
  }, []);

  const saveToken = async (tokenObject: string) => {
    await SecureStore.setItemAsync('userToken', tokenObject);
    setToken(tokenObject);
  };

  return { token, saveToken };
};
