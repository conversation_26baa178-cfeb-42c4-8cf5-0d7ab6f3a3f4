// import { MutableRefObject, useState } from 'react';
// import { Alert } from 'react-native';
// import WebView from 'react-native-webview';
// import { copyToClipboard } from '../services/clipboardService';
// import { downloadFromAPI, savePDF } from '../services/fileService';
// import { openUrlBrowser } from '../services/urlService';
// import { useAuth } from './useAuth';

// export const useWebViewHandlers = (webViewRef: MutableRefObject<WebView | null>) => {
//   console.log('::: useWebViewHandlers >> Constructor :: webViewRef', webViewRef);

//   // const [url, setUrl] = useState('https://cliente.cury.net');
//   // const [url, setUrl] = useState(`https://cliente.cury.net?t=${new Date().getTime()}`);
//   const [url, setUrl] = useState(`************:3000?t=${new Date().getTime()}`);
//   const { authenticate } = useBiometricAuth(webViewRef);
//   const { saveToken } = useAuth(webViewRef);

//   const handleMessage = async (event: any) => {
//     const data = event.nativeEvent.data;

//     try {
//       const dataObj = JSON.parse(data);
//       switch (dataObj.type) {
//         case 'copyText':
//           await copyToClipboard(dataObj.data.text);
//           break;
//         case 'downloadFromAPI':
//           await downloadFromAPI(dataObj.data.path, dataObj.data.namefile);
//           break;
//         case 'openUrlBrowser':
//           await openUrlBrowser(dataObj.url);
//           break;
//         case 'changeEnv':
//           changeEnv();
//           break;
//         case 'downloadPDF':
//           await savePDF(dataObj.data.base64, dataObj.data.filename);
//           break;
//         case 'checkBiometricAccess':
//           await authenticate();
//           break;
//         case 'credentialsRequired':
//           Alert.alert(
//             'Utilize suas credenciais',
//             'Para sua segurança, é necessário acessar utilizando suas credenciais usuário e senha.',
//             [{ text: 'OK', onPress: () => {} }]
//           );
//           break;
//         case 'saveToken':
//           await saveToken(dataObj.data);
//           break;
//       }
//     } catch (error) {
//       console.error('Failed to parse JSON string:', error);
//     }

//     console.log('handleMessage sucesso!');
//   };

//   const changeEnv = () => {
//     setUrl(
//       url === 'https://cliente.cury.net'
//         ? 'https://homolog.cliente.cury.net'
//         : 'https://cliente.cury.net'
//     );
//   };

//   return { url, handleMessage };
// };

import { MutableRefObject, useState } from 'react';
import { Alert } from 'react-native';
import WebView from 'react-native-webview';
import { getWebViewUrl } from '../constants/Environment';
import { copyToClipboard } from '../services/clipboardService';
import { downloadFromAPI, savePDF } from '../services/fileService';
import { openUrlBrowser } from '../services/urlService';
import { useAuth } from './useAuth';
import { useBiometricAuth } from './useBiometricAuth';

export const useWebViewHandlers = (webViewRef: MutableRefObject<WebView | null>) => {
  console.log('::: useWebViewHandlers >> Constructor :: webViewRef', webViewRef);

  // Initialize URL based on environment configuration
  const [url, setUrl] = useState(getWebViewUrl());
  const { authenticate } = useBiometricAuth(webViewRef);
  const { saveToken } = useAuth(webViewRef);

  const handleMessage = async (event: any) => {
    const data = event.nativeEvent.data;

    try {
      const dataObj = JSON.parse(data);

      // Processa mensagens de debug do WebView
      if (dataObj.type === 'webview-debug') {
        switch (dataObj.logType) {
          case 'log':
            console.log('WebView console.log:', ...dataObj.data);
            break;
          case 'error':
            console.error('WebView console.error:', ...dataObj.data);
            break;
          case 'warn':
            console.warn('WebView console.warn:', ...dataObj.data);
            break;
          case 'uncaught-error':
            console.error('WebView uncaught error:', dataObj.data);
            break;
        }
        // Retorna aqui para não processar mais
        return;
      }

      // Processamento existente
      switch (dataObj.type) {
        case 'copyText':
          await copyToClipboard(dataObj.data.text);
          break;
        case 'downloadFromAPI':
          await downloadFromAPI(dataObj.data.path, dataObj.data.namefile);
          break;
        case 'openUrlBrowser':
          await openUrlBrowser(dataObj.url);
          break;
        case 'changeEnv':
          changeEnv();
          break;
        case 'downloadPDF':
          await savePDF(dataObj.data.base64, dataObj.data.filename);
          break;
        case 'checkBiometricAccess':
          await authenticate();
          break;
        case 'credentialsRequired':
          Alert.alert(
            'Utilize suas credenciais',
            'Para sua segurança, é necessário acessar utilizando suas credenciais usuário e senha.',
            [{ text: 'OK', onPress: () => {} }]
          );
          break;
        case 'saveToken':
          await saveToken(dataObj.data);
          break;
      }
    } catch (error) {
      console.error('Failed to parse JSON string:', error);
    }

    console.log('handleMessage sucesso!');
  };

  const changeEnv = () => {
    // Toggle between production and staging URLs
    const currentUrl = url.split('?')[0]; // Remove timestamp parameter
    if (currentUrl.includes('homolog.cliente.cury.net')) {
      setUrl(`https://cliente.cury.net?t=${new Date().getTime()}`);
    } else {
      setUrl(`https://homolog.cliente.cury.net?t=${new Date().getTime()}`);
    }
  };

  return { url, handleMessage };
};
