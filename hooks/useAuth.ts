import React, { useState, useRef, MutableRefObject } from "react";
import * as SecureStore from "expo-secure-store";
import { WebView } from "react-native-webview";

export const useAuth = (webViewRef: MutableRefObject<WebView | null>) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(false);

  const initializeAuth = async () => {
    const storedToken = await loadToken();
    if (storedToken) {
      setIsAuthenticated(true);
    }
  };

  const saveToken = async (tokenObject: string) => {
    await SecureStore.setItemAsync("userToken", tokenObject);
    console.log(
      "::: saveToken >>  SecureStore userToken item",
      await SecureStore.getItemAsync("userToken")
    );
  };

  const authWithToken = async () => {
    const storedToken = await loadToken();
    if (!storedToken) return;

    if (webViewRef.current) {
      const jsCode = `window.receiveUserData(${JSON.stringify(
        storedToken
      )}); true;`;
      webViewRef.current.injectJavaScript(jsCode);
      setIsAuthenticated(true);

      console.log("::: authWithToken >> Data SENT to WebView ");
    } else {
      console.log("::: WebView is null");
    }
  };

  const loadToken = async (): Promise<string | null> => {
    return await SecureStore.getItemAsync("userToken");
  };

  return {
    saveToken,
    authWithToken,
    isAuthenticated,
    setIsAuthenticated,
    loadToken,
    initializeAuth,
  };
};
