// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EnvironmentTag should render development tag correctly 1`] = `
<View
  pointerEvents="none"
  style={
    [
      {
        "left": 10,
        "position": "absolute",
        "zIndex": 9999,
      },
      {
        "top": 54,
      },
    ]
  }
  testID="environment-tag-container"
>
  <View
    style={
      [
        {
          "borderRadius": 12,
          "borderWidth": 1,
          "elevation": 5,
          "paddingHorizontal": 8,
          "paddingVertical": 4,
          "shadowColor": "#000",
          "shadowOffset": {
            "height": 2,
            "width": 0,
          },
          "shadowOpacity": 0.25,
          "shadowRadius": 3.84,
        },
        {
          "backgroundColor": "#FF6B35",
          "borderColor": "#E55A2B",
        },
      ]
    }
    testID="environment-tag"
  >
    <Text
      style={
        {
          "color": "#FFFFFF",
          "fontSize": 10,
          "fontWeight": "600",
          "letterSpacing": 0.5,
          "textTransform": "uppercase",
        }
      }
      testID="environment-tag-text"
    >
      Desenvolvimento
    </Text>
  </View>
</View>
`;
