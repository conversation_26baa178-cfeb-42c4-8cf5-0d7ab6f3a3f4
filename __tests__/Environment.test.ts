import { 
  getCurrentEnvironment, 
  getEnvironmentConfig, 
  getWebViewUrl, 
  getApiBaseUrl,
  isProduction,
  isStaging,
  isDevelopment,
  getEnvironmentDisplayName 
} from '../constants/Environment';

// Mock expo-constants
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      environment: 'development'
    }
  }
}));

describe('Environment Configuration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should return development environment by default', () => {
    expect(getCurrentEnvironment()).toBe('development');
  });

  test('should return correct environment config for development', () => {
    const config = getEnvironmentConfig();
    expect(config.environment).toBe('development');
    expect(config.webViewUrl).toContain('192.168.1.95:3000');
    expect(config.apiBaseUrl).toBe('http://192.168.1.95:3000/api');
  });

  test('should return correct WebView URL', () => {
    const url = getWebViewUrl();
    expect(url).toContain('192.168.1.95:3000');
    expect(url).toContain('t='); // Should include timestamp
  });

  test('should return correct API base URL', () => {
    const url = getApiBaseUrl();
    expect(url).toBe('http://192.168.1.95:3000/api');
  });

  test('should correctly identify development environment', () => {
    expect(isDevelopment()).toBe(true);
    expect(isStaging()).toBe(false);
    expect(isProduction()).toBe(false);
  });

  test('should return correct display name for development', () => {
    expect(getEnvironmentDisplayName()).toBe('Desenvolvimento');
  });

  test('should return environment info object', () => {
    const { getEnvironmentInfo } = require('../constants/Environment');
    const info = getEnvironmentInfo();
    
    expect(info).toHaveProperty('environment', 'development');
    expect(info).toHaveProperty('webViewUrl');
    expect(info).toHaveProperty('apiBaseUrl');
    expect(info).toHaveProperty('displayName', 'Desenvolvimento');
    expect(info).toHaveProperty('isProduction', false);
    expect(info).toHaveProperty('isStaging', false);
    expect(info).toHaveProperty('isDevelopment', true);
  });
});

describe('Environment Configuration - Staging', () => {
  beforeEach(() => {
    // Mock staging environment
    jest.doMock('expo-constants', () => ({
      expoConfig: {
        extra: {
          environment: 'staging'
        }
      }
    }));
  });

  afterEach(() => {
    jest.resetModules();
  });

  test('should handle staging environment correctly', () => {
    // Re-import to get mocked version
    const { getCurrentEnvironment, getEnvironmentConfig } = require('../constants/Environment');
    
    expect(getCurrentEnvironment()).toBe('staging');
    
    const config = getEnvironmentConfig();
    expect(config.environment).toBe('staging');
    expect(config.webViewUrl).toContain('homolog.cliente.cury.net');
    expect(config.apiBaseUrl).toBe('https://homolog.cliente.cury.net/api');
  });
});

describe('Environment Configuration - Production', () => {
  beforeEach(() => {
    // Mock production environment
    jest.doMock('expo-constants', () => ({
      expoConfig: {
        extra: {
          environment: 'production'
        }
      }
    }));
  });

  afterEach(() => {
    jest.resetModules();
  });

  test('should handle production environment correctly', () => {
    // Re-import to get mocked version
    const { getCurrentEnvironment, getEnvironmentConfig } = require('../constants/Environment');
    
    expect(getCurrentEnvironment()).toBe('production');
    
    const config = getEnvironmentConfig();
    expect(config.environment).toBe('production');
    expect(config.webViewUrl).toContain('cliente.cury.net');
    expect(config.apiBaseUrl).toBe('https://cliente.cury.net/api');
  });
});
