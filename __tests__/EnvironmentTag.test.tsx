import React from 'react';
import renderer from 'react-test-renderer';
import EnvironmentTag from '../components/EnvironmentTag';

// Mock expo-constants
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      environment: 'development'
    }
  }
}));

// Mock react-native-safe-area-context
jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({
    top: 44,
    bottom: 0,
    left: 0,
    right: 0,
  }),
}));

describe('EnvironmentTag', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render development tag correctly', () => {
    const tree = renderer.create(<EnvironmentTag />).toJSON();
    expect(tree).toBeTruthy();

    // Check if the component structure is correct
    expect(tree).toMatchSnapshot();
  });

  test('should render without errors', () => {
    const component = renderer.create(<EnvironmentTag />);
    expect(component.toJSON()).toBeTruthy();
  });

  test('should have correct testIDs', () => {
    const component = renderer.create(<EnvironmentTag />);
    const tree = component.toJSON();

    // The component should render with the expected structure
    expect(tree).toBeTruthy();
  });
});
