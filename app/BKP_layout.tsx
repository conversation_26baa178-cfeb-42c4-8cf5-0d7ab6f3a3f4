// import { useFonts } from 'expo-font';
// import * as SplashScreen from 'expo-splash-screen';
// import { useEffect, useRef, useState } from 'react';
// import 'react-native-reanimated';

// import { Linking, NativeModules, Platform, StyleSheet } from 'react-native';
// import { GestureHandlerRootView } from 'react-native-gesture-handler';
// // import { OneSignal } from 'react-native-onesignal';
// import { SafeAreaView } from 'react-native-safe-area-context';

// import { WebView } from 'react-native-webview';

// // import Clipboard from '@react-native-clipboard/clipboard';
// import * as Clipboard from 'expo-clipboard';

// import * as FileSystem from 'expo-file-system';
// import { shareAsync } from 'expo-sharing';

// SplashScreen.preventAutoHideAsync();

// export default function BKP_RootLayout() {
//     const [loaded] = useFonts({
//         SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
//     });

//     // OneSignal.initialize('************************************');
//     // OneSignal.Notifications.requestPermission(true);
//     // OneSignal.Notifications.addEventListener('click', (event) => {
//     //   console.log('OneSignal: notification clicked:', event);
//     // });

//     console.log(loaded);
//     const webviewRef = useRef(null);

//     useEffect(() => {
//         // if (loaded) {
//         SplashScreen.hideAsync();
//         // }
//     }, [loaded]);

//     if (!loaded) {
//         // return null;
//     }

//     const downloadFromAPI = async (path: string, filename: string) => {
//         console.log('downloadFromAPI');
//         console.log(path);
//         console.log(filename);
//         const result = await FileSystem.downloadAsync(
//             path,
//             FileSystem.documentDirectory + filename,
//             {
//                 headers: {
//                     MyHeader: 'MyValue',
//                 },
//             },
//         );

//         save(result.uri, filename, result.headers['Content-Type']);
//     };

//     const save = async (uri: string, filename: string, mimetype: string) => {
//         if (Platform.OS === 'android') {
//             const permissions =
//                 await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();

//             if (permissions.granted) {
//                 const base64 = await FileSystem.readAsStringAsync(uri, {
//                     encoding: FileSystem.EncodingType.Base64,
//                 });
//                 await FileSystem.StorageAccessFramework.createFileAsync(
//                     permissions.directoryUri,
//                     filename,
//                     mimetype,
//                 )
//                     .then(async (uri) => {
//                         await FileSystem.writeAsStringAsync(uri, base64, {
//                             encoding: FileSystem.EncodingType.Base64,
//                         });
//                     })
//                     .catch((e) => console.log(e));
//             } else {
//                 shareAsync(uri);
//             }

//             const downloadManager = NativeModules.DownloadManager;
//         } else {
//             shareAsync(uri);
//         }
//     };

//     const handleMessage = async (event: any) => {
//         const data = event.nativeEvent.data;

//         try {
//             const dataObj = JSON.parse(data);
//             switch (dataObj.type) {
//                 case 'copyText':
//                     copyToClipboard(dataObj.data.text);
//                     break;
//                 case 'downloadFromAPI':
//                     downloadFromAPI(dataObj.data.path, dataObj.data.namefile);
//                     break;
//                 case 'openUrlBrowser':
//                     openUrlBrowser(dataObj.url);
//                     break;
//                 case 'changeEnv':
//                     changeEnv();
//                     break;
//                 case 'downloadPDF':
//                     await savePDF(dataObj.data.base64, dataObj.data.filename);
//                     break;
//             }
//         } catch (error) {
//             console.error('Failed to parse JSON string:', error);
//         }

//         console.log('handleMessage sucesso!');
//     };
//     const allowedDomains = [
//         'http://***********:3000',
//         'https://manual1.com.br',
//         "https://api.sienge.com.br/curyempreendimentos/public/api/",
//         "https://api.sienge.com.br/curyhomolog/public/api/",
//         'https://sigmacivil.com.br',
//         'https://cliente.cury.net',
//         'https://curyconstrutora.sandbox.lightning.force.com',
//         'https://curyconstrutora.sandbox.my.salesforce-scrt.com',
//         'https://curyconstrutora.sandbox.my.site.com',
//         'https://*.cury.net',
//         'https://homolog.cliente.cury.net',
//         'https://homolog.api.cliente.cury.net',
//         'https://curyconstrutora--uatfull.sandbox.lightning.force.com',
//         'https://curyconstrutora--uatfull.sandbox.my.salesforce-scrt.com',
//         'https://curyconstrutora--uatfull.sandbox.my.site.com',
//         'https://curyconstrutora.lightning.force.com',
//         'https://curyconstrutora.my.salesforce-scrt.com',
//         'https://curyconstrutora.my.site.com',
//         'https://youtube.com.br',
//         'https://youtube.com',
//         'https://www.youtube.com',
//         'https://www.youtube.com.br',
//         'https://s3clienteappcury.s3.amazonaws.com',
//         'https://*.s3.amazonaws.com',
//         'https://*.amazonaws.com'
//     ];
//     const handleRequest = (request: any) => {
//         return allowedDomains.some((domain) => request.url.startsWith(domain));
//     };

//     const savePDF = async (base64Data: string, filename: string) => {
//         const fileUri = FileSystem.documentDirectory + filename;

//         try {
//             // Remover o prefixo 'data:application/pdf;base64,' se presente
//             const pdfData = base64Data.split(',')[1] || base64Data;

//             await FileSystem.writeAsStringAsync(fileUri, pdfData, {
//                 encoding: FileSystem.EncodingType.Base64,
//             });

//             if (Platform.OS === 'android') {
//                 const permissions =
//                     await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();

//                 if (permissions.granted) {
//                     await FileSystem.StorageAccessFramework.createFileAsync(
//                         permissions.directoryUri,
//                         filename,
//                         'application/pdf'
//                     )
//                         .then(async (uri) => {
//                             const pdfContent = await FileSystem.readAsStringAsync(fileUri, {
//                                 encoding: FileSystem.EncodingType.Base64,
//                             });
//                             await FileSystem.writeAsStringAsync(uri, pdfContent, {
//                                 encoding: FileSystem.EncodingType.Base64,
//                             });
//                             console.log('PDF saved successfully');
//                         })
//                         .catch((e) => console.log(e));
//                 } else {
//                     await shareAsync(fileUri);
//                 }
//             } else {
//                 await shareAsync(fileUri);
//             }
//         } catch (error) {
//             console.error('Error saving PDF:', error);
//         }
//     };

//     const openUrlBrowser = async (url: string) => {
//         Linking.openURL(url);
//     };

//     const copyToClipboard = async (text: string) => {
//         try {
//             await Clipboard.setStringAsync(text);

//             console.log('Texto copiado para a área de transferência: ' + text);
//         } catch (err) {
//             console.error('Algo deu errado ao copiar', err);
//         }
//     };
//     const [url, setUrl] = useState('https://cliente.cury.net');
//     const changeEnv = async () => {
//         try {
//             setUrl(
//                 url === 'https://cliente.cury.net'
//                     ? 'https://homolog.cliente.cury.net'
//                     : 'https://cliente.cury.net',
//             );
//         } catch (err) {
//             console.error('Algo deu errado ao copiar', err);
//         }
//     };
//     return (
//         <GestureHandlerRootView style={styles.container}>
//             <SafeAreaView style={styles.container}>
//                 <WebView
//                     javaScriptEnabled={true}
//                     allowsInlineMediaPlayback={true}
//                     allow="microphone; camera;"
//                     ref={webviewRef}
//                     setLoadWithOverviewMode={true}
//                     style={styles.container}
//                     // source={{ uri: url }}
//                     source={{ uri: 'http://***********:3000' }}
//                     injectedJavaScript={`document.documentElement.lang = 'pt-BR';`}
//                     onShouldStartLoadWithRequest={handleRequest}
//                     onMessage={handleMessage}
//                     incognito={true}
//                 />
//             </SafeAreaView>
//         </GestureHandlerRootView>
//     );
// }

// const styles = StyleSheet.create({
//     container: {
//         flex: 1,
//     },
// });
