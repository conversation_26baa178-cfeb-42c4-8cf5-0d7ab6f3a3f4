import * as SplashScreen from 'expo-splash-screen';
import React, { useEffect } from 'react';
import { AppRegistry, StyleSheet } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import WebViewComponent from '../components/WebViewComponent';
import { useFonts } from '../hooks/useFonts';
import { useWebViewHandlers } from '../hooks/useWebViewHandlers';
AppRegistry.registerComponent('main', () => WebViewComponent);

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded] = useFonts();
  const { url, handleMessage } = useWebViewHandlers(null);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  console.log('url');
  console.log(url);

  if (!loaded) {
    return null;
  }
  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <WebViewComponent url={url} onMessage={handleMessage} />
      </SafeAreaView>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  }
});
