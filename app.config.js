export default ({ config }) => {
  const environment = process.env.EXPO_PUBLIC_ENVIRONMENT || 'development';
  
  // Base configuration
  const baseConfig = {
    expo: {
      name: environment === 'staging' ? 'Cury Cliente (Homolog)' : 'Cury Cliente',
      slug: environment === 'staging' ? 'cury-cliente-staging' : 'cury-cliente',
      version: '2.2',
      newArchEnabled: true,
      orientation: 'portrait',
      icon: './assets/images/icon_cury_cliente2.png',
      scheme: environment === 'staging' ? 'cury-cliente-staging' : 'cury-cliente',
      userInterfaceStyle: 'light',
      splash: {
        image: './assets/images/splash-cury-cliente.png',
        resizeMode: 'contain',
        backgroundColor: '#61829a'
      },
      ios: {
        supportsTablet: true,
        bundleIdentifier: environment === 'staging' ? 'net.cury.cliente.staging' : 'net.cury.cliente',
        buildNumber: '36',
        infoPlist: {
          ITSAppUsesNonExemptEncryption: false
        }
      },
      android: {
        adaptiveIcon: {
          foregroundImage: './assets/images/icon_cury_cliente2.png',
          backgroundColor: '#61829a'
        },
        permissions: [
          'android.permission.CAMERA',
          'android.permission.RECORD_AUDIO',
          'android.permission.USE_BIOMETRIC',
          'android.permission.USE_FINGERPRINT'
        ],
        package: environment === 'staging' ? 'net.cury.cliente.staging' : 'net.cury.cliente',
        versionCode: 16
      },
      plugins: [
        [
          'expo-local-authentication',
          {
            faceIDPermission: 'Habilitar $(PRODUCT_NAME) utilizar Face ID.'
          }
        ],
        [
          'expo-camera',
          {
            cameraPermission: 'Habilitar $(PRODUCT_NAME) acesso à câmera',
            microphonePermission: 'Habilitar $(PRODUCT_NAME) acesso ao microfone',
            recordAudioAndroid: true
          }
        ],
        [
          'onesignal-expo-plugin',
          {
            mode: environment === 'production' ? 'production' : 'development'
          }
        ],
        [
          'expo-build-properties',
          {
            android: {
              enableProguardInReleaseBuilds: true,
              enableShrinkResourcesInReleaseBuilds: true
            }
          }
        ],
        'expo-build-properties',
        'expo-font',
        'expo-router',
        'expo-secure-store'
      ],
      extra: {
        eas: {
          projectId: 'c44eb527-a776-473b-a627-e37390e7da4b'
        },
        oneSignalAppId: '************************************',
        environment: environment
      }
    }
  };

  return baseConfig;
};
