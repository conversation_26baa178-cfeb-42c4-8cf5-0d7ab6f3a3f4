{"name": "cury-cliente", "main": "expo-router/entry", "version": "1.0.2", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^6.3.29", "expo": "~52.0.42", "expo-build-properties": "~0.13.2", "expo-camera": "~16.0.18", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.8", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-linking": "~7.0.5", "expo-local-authentication": "~15.0.2", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "onesignal-expo-plugin": "^2.0.3", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-onesignal": "^5.2.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "^0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.1.6", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.0.7", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true, " expo.doctor.reactNativeDirectoryCheck.listUnknownPackages": false, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}