import Constants from 'expo-constants';

// Environment types
export type Environment = 'development' | 'staging' | 'production';

// Get current environment from Expo Constants
export const getCurrentEnvironment = (): Environment => {
  const releaseChannel = Constants.expoConfig?.extra?.environment;

  if (releaseChannel === 'staging') {
    return 'staging';
  } else if (releaseChannel === 'production') {
    return 'production';
  } else {
    return 'development';
  }
};

// Environment-specific configuration
const environmentConfig = {
  development: {
    webViewUrl: `http://192.168.1.95:3000?t=${new Date().getTime()}`,
    apiBaseUrl: 'http://192.168.1.95:3000/api',
    environment: 'development' as Environment,
  },
  staging: {
    webViewUrl: `https://homolog.cliente.cury.net?t=${new Date().getTime()}`,
    apiBaseUrl: 'https://homolog.cliente.cury.net/api',
    environment: 'staging' as Environment,
  },
  production: {
    webViewUrl: `https://cliente.cury.net?t=${new Date().getTime()}`,
    apiBaseUrl: 'https://cliente.cury.net/api',
    environment: 'production' as Environment,
  },
};

// Get current environment config
export const getEnvironmentConfig = () => {
  const currentEnv = getCurrentEnvironment();
  return environmentConfig[currentEnv];
};

// Helper functions for specific values
export const getWebViewUrl = (): string => {
  return getEnvironmentConfig().webViewUrl;
};

export const getApiBaseUrl = (): string => {
  return getEnvironmentConfig().apiBaseUrl;
};

export const isProduction = (): boolean => {
  return getCurrentEnvironment() === 'production';
};

export const isStaging = (): boolean => {
  return getCurrentEnvironment() === 'staging';
};

export const isDevelopment = (): boolean => {
  return getCurrentEnvironment() === 'development';
};

// Environment display names
export const getEnvironmentDisplayName = (): string => {
  const env = getCurrentEnvironment();
  switch (env) {
    case 'production':
      return 'Produção';
    case 'staging':
      return 'Homologação';
    case 'development':
      return 'Desenvolvimento';
    default:
      return 'Desenvolvimento';
  }
};

// Debug information
export const getEnvironmentInfo = () => {
  const config = getEnvironmentConfig();
  return {
    environment: config.environment,
    webViewUrl: config.webViewUrl,
    apiBaseUrl: config.apiBaseUrl,
    displayName: getEnvironmentDisplayName(),
    isProduction: isProduction(),
    isStaging: isStaging(),
    isDevelopment: isDevelopment(),
  };
};
