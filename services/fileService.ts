import * as FileSystem from 'expo-file-system';
import { shareAsync } from 'expo-sharing';
import { Platform } from 'react-native';

export const downloadFromAPI = async (path: string, filename: string) => {
  console.log('downloadFromAPI');
  console.log(path);
  console.log(filename);
  const result = await FileSystem.downloadAsync(path, FileSystem.documentDirectory + filename, {
    headers: {
      MyHeader: 'MyValue'
    }
  });

  save(result.uri, filename, result.headers['Content-Type']);
};

const save = async (uri: string, filename: string, mimetype: string) => {
  if (Platform.OS === 'android') {
    const permissions = await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();

    if (permissions.granted) {
      const base64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64
      });
      await FileSystem.StorageAccessFramework.createFileAsync(
        permissions.directoryUri,
        filename,
        mimetype
      )
        .then(async (uri) => {
          await FileSystem.writeAsStringAsync(uri, base64, {
            encoding: FileSystem.EncodingType.Base64
          });
        })
        .catch((e) => console.log(e));
    } else {
      shareAsync(uri);
    }
  } else {
    shareAsync(uri);
  }
};

export const savePDF = async (base64Data: string, filename: string) => {
  const fileUri = FileSystem.documentDirectory + filename;

  try {
    const pdfData = base64Data.split(',')[1] || base64Data;

    await FileSystem.writeAsStringAsync(fileUri, pdfData, {
      encoding: FileSystem.EncodingType.Base64
    });

    if (Platform.OS === 'android') {
      try {
        // On Android, we'll use the Storage Access Framework to save to Downloads
        // Get the Downloads directory URI
        const permissions =
          await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync();

        if (permissions.granted) {
          // Read the PDF content
          const pdfContent = await FileSystem.readAsStringAsync(fileUri, {
            encoding: FileSystem.EncodingType.Base64
          });

          // Create the file in the selected directory (typically Downloads)
          const destinationUri = await FileSystem.StorageAccessFramework.createFileAsync(
            permissions.directoryUri,
            filename,
            'application/pdf'
          );

          // Write the content to the file
          await FileSystem.writeAsStringAsync(destinationUri, pdfContent, {
            encoding: FileSystem.EncodingType.Base64
          });

          console.log('PDF saved successfully to Downloads');
        } else {
          // Fallback to sharing if permissions not granted
          await shareAsync(fileUri);
        }
      } catch (error) {
        console.error('Error saving PDF to Downloads:', error);
        // Fallback to sharing if there's an error
        await shareAsync(fileUri);
      }
    } else if (Platform.OS === 'ios') {
      try {
        // On iOS, we'll save to a temporary location and then use shareAsync
        // with the 'Save to Files' option which allows saving to Downloads
        await shareAsync(fileUri, {
          UTI: 'com.adobe.pdf',
          mimeType: 'application/pdf'
        });
        console.log('PDF shared for saving on iOS');
      } catch (error) {
        console.error('Error sharing PDF on iOS:', error);
      }
    }
  } catch (error) {
    console.error('Error saving PDF:', error);
  }
};
