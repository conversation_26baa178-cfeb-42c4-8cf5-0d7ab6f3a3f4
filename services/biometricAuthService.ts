import * as LocalAuthentication from 'expo-local-authentication';
import { Alert } from 'react-native';

export const biometricAuthHandler = async (setIsAuthenticated: Function ) => {
  
  async function checkAvailableAuthentication() {
    const compatible = await LocalAuthentication.hasHardwareAsync();
    console.log('compatible', compatible);

    if (compatible) {
      const supportedAuthTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      console.log('supportedAuthTypes', supportedAuthTypes.map((type) => LocalAuthentication.AuthenticationType[type]));
    }

    return compatible;
  }

  async function handleAuthentication() {
    const isBiometricEnrolled = await LocalAuthentication.isEnrolledAsync();
    if (!isBiometricEnrolled) {
      return Alert.alert('Biometria não cadastrada', 'Por favor, cadastre sua biometria nas configurações do dispositivo.');
    }

    const result = await LocalAuthentication.authenticateAsync({
      promptMessage: 'Confirme sua identidade',
      fallbackLabel: 'Identidade não reconhecida. Tente novamente.',
      cancelLabel: 'Cancelar',
      disableDeviceFallback: true,
    });
    console.log('result', result);
    
    setIsAuthenticated(result.success);

    return result.success;
  }

  try {
    const hasHardware = await checkAvailableAuthentication();
    if (!hasHardware) {
      return false;
    }
    
    return handleAuthentication();
  } catch (err) {
    console.error('Erro :', err);
    return false;
  }
};
